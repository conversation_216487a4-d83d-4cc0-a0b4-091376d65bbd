# API Documentation Validation Corrections

## Executive Summary

This document provides corrected Request Body and Success Response definitions for the Selltis Web API documentation, ensuring alignment with the actual Selltis metadata architecture and MD table structure.

## Critical Findings

### ❌ Major Misalignments Identified

1. **Form Field Layout Properties**: Documentation assumes individual field layout properties are stored in metadata (rowNumber, columnNumber, colSpan, labelWidth, etc.) - **These don't exist in Selltis metadata**
2. **Form Tab Individual Properties**: Documentation treats tabs as individual objects with properties - **Tabs are stored as comma-separated values in TABS metadata property**
3. **UI/Presentation Properties**: Many properties are UI-specific and not stored in the core metadata system
4. **Database Table Properties**: Assumes table metadata is directly accessible - **Requires metadata system queries**

## Corrected API Definitions

### 1. Form Field Management - CORRECTED

#### ❌ INCORRECT - Add Field to Form Request Body
```json
{
  "hostName": "demo.selltis.com",
  "fieldName": "EML_EMAIL",
  "tabId": "TAB_CONTACT",
  "rowNumber": 5,           // ❌ NOT in metadata
  "columnNumber": 1,        // ❌ NOT in metadata
  "colSpan": 2,            // ❌ NOT in metadata
  "rowSpan": 1,            // ❌ NOT in metadata
  "state": 0,              // ❌ NOT in metadata
  "labelAbove": false,     // ❌ NOT in metadata
  "labelWidth": 150,       // ❌ NOT in metadata
  "controlWidth": 300,     // ❌ NOT in metadata
  "section": "GLOBAL"
}
```

#### ✅ CORRECTED - Add Field to Form Request Body
```json
{
  "hostName": "demo.selltis.com",
  "fieldName": "EML_EMAIL",
  "section": "GLOBAL",
  "insertAfterField": "TXT_COMPANYNAME",  // Position relative to existing field
  "tabId": "TAB_CONTACT"                  // Tab assignment (if tabs exist)
}
```

#### ❌ INCORRECT - Get Form Fields Success Response
```json
{
  "formId": "FRM_COMPANY",
  "fields": [
    {
      "fieldName": "TXT_CODE",
      "fieldLabel": "Company Code",
      "fieldType": "TXT",
      "tabId": "TAB_GENERAL",
      "rowNumber": 1,           // ❌ NOT in metadata
      "columnNumber": 1,        // ❌ NOT in metadata
      "colSpan": 1,            // ❌ NOT in metadata
      "rowSpan": 1,            // ❌ NOT in metadata
      "state": 0,              // ❌ NOT in metadata
      "labelAbove": false,     // ❌ NOT in metadata
      "labelWidth": 150,       // ❌ NOT in metadata
      "controlWidth": 200,     // ❌ NOT in metadata
      "isRequired": true,      // ❌ NOT in form metadata
      "isReadOnly": false,     // ❌ NOT in form metadata
      "isVisible": true,       // ❌ NOT in form metadata
      "tooltip": "Enter company code"  // ❌ NOT in metadata
    }
  ]
}
```

#### ✅ CORRECTED - Get Form Fields Success Response
```json
{
  "formId": "FRM_COMPANY",
  "section": "GLOBAL",
  "tableName": "CO",
  "fields": [
    {
      "fieldName": "TXT_CODE",
      "fieldOrder": 1,                    // Position in FIELD1, FIELD2, etc.
      "tabAssignment": "TAB_GENERAL"      // From tab analysis if tabs exist
    },
    {
      "fieldName": "TXT_COMPANYNAME", 
      "fieldOrder": 2,
      "tabAssignment": "TAB_GENERAL"
    }
  ],
  "tabs": ["TAB_GENERAL", "TAB_CONTACT", "TAB_ADDRESS"],  // From TABS metadata
  "totalFieldCount": 25,
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z",
  "createdBy": "12345678-1234-1234-1234-123456789012",
  "modifiedBy": "ADMIN"
}
```

### 2. Form Tab Management - CORRECTED

#### ❌ INCORRECT - Get Form Tabs Success Response
```json
{
  "formId": "FRM_COMPANY",
  "tabs": [
    {
      "tabId": "TAB_GENERAL",
      "tabName": "General",        // ❌ NOT stored separately
      "orderIndex": 1,             // ❌ Derived from TABS order
      "fieldCount": 15,            // ❌ Calculated, not stored
      "isVisible": true,           // ❌ NOT in metadata
      "tooltip": "General company information"  // ❌ NOT in metadata
    }
  ]
}
```

#### ✅ CORRECTED - Get Form Tabs Success Response
```json
{
  "formId": "FRM_COMPANY",
  "section": "GLOBAL",
  "tabsMetadata": "TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS",  // Raw TABS property
  "tabs": [
    {
      "tabId": "TAB_GENERAL",
      "orderIndex": 1              // Position in comma-separated list
    },
    {
      "tabId": "TAB_CONTACT", 
      "orderIndex": 2
    },
    {
      "tabId": "TAB_ADDRESS",
      "orderIndex": 3
    }
  ],
  "totalTabCount": 3,
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z"
}
```

#### ❌ INCORRECT - Add Tab to Form Request Body
```json
{
  "hostName": "demo.selltis.com",
  "tabId": "TAB_CUSTOM",
  "tabName": "Custom Fields",     // ❌ NOT stored in metadata
  "orderIndex": 3,                // ❌ Position handled by comma-separated list
  "isVisible": true,              // ❌ NOT in metadata
  "tooltip": "Custom field information",  // ❌ NOT in metadata
  "section": "GLOBAL"
}
```

#### ✅ CORRECTED - Add Tab to Form Request Body
```json
{
  "hostName": "demo.selltis.com",
  "tabId": "TAB_CUSTOM",
  "insertAfterTab": "TAB_CONTACT",  // Position relative to existing tab
  "section": "GLOBAL"
}
```

### 3. Form Metadata Operations - CORRECTLY ALIGNED ✅

#### ✅ CORRECT - Get Form Metadata Success Response
```json
{
  "formId": "FRM_COMPANY",
  "section": "GLOBAL", 
  "metadata": {
    "FILE": "CO",                    // ✅ Actual metadata property
    "NAME": "Company Form",          // ✅ Actual metadata property
    "TYPE": "EDIT",                  // ✅ Actual metadata property
    "SHOWSTATUSBAR": "1",           // ✅ Actual metadata property
    "STATUSBARFIELD": "TXT_CODE",   // ✅ Actual metadata property
    "TABS": "TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS",  // ✅ Actual metadata
    "FIELD1": "TXT_CODE",           // ✅ Actual metadata property
    "FIELD2": "TXT_COMPANYNAME",    // ✅ Actual metadata property
    "CUSTOMFIELD1": "value1"        // ✅ Custom metadata property
  },
  "rawMetadata": "[Complete metadata in INI format]",
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z",
  "createdBy": "12345678-1234-1234-1234-123456789012",
  "modifiedBy": "ADMIN",
  "product": "SA"
}
```

### 4. Database Table Operations - PARTIALLY CORRECTED

#### ❌ INCORRECT - Get Table Details Success Response
```json
{
  "tableName": "CO",
  "hasPermissions": true,    // ❌ Requires metadata lookup
  "hasForm": true,          // ❌ Requires metadata lookup  
  "hasDesktop": true,       // ❌ Requires metadata lookup
  "recordCount": 1250       // ❌ Expensive COUNT query
}
```

#### ✅ CORRECTED - Get Table Details Success Response
```json
{
  "tableName": "CO",
  "tableLabel": "Companies",
  "tableLabelPlural": "Companies", 
  "fieldCount": 45,
  "primaryKeyField": "GID_COMPANY",
  "fields": [
    {
      "fieldName": "GID_COMPANY",
      "fieldLabel": "Company ID",
      "fieldType": "GID",
      "length": 36,
      "isRequired": true,
      "isPrimaryKey": true
    }
  ],
  // Note: hasForm, hasDesktop, recordCount require separate API calls
  // due to performance and architectural considerations
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z"
}
```

## Properties to Comment Out or Remove

### Form Field Management Endpoints
**Comment out these properties** (not stored in metadata):
- `rowNumber`, `columnNumber`, `colSpan`, `rowSpan`
- `state`, `labelAbove`, `labelWidth`, `controlWidth`
- `isRequired`, `isReadOnly`, `isVisible`, `tooltip`

### Form Tab Management Endpoints  
**Comment out these properties** (not stored in metadata):
- `tabName`, `fieldCount`, `isVisible`, `tooltip`
- Individual tab property objects (tabs are comma-separated values)

### Database Table Endpoints
**Comment out these properties** (require expensive queries):
- `recordCount` (requires COUNT query)
- `hasForm`, `hasDesktop` (require metadata system queries)

## Validation Rules Corrections

### Form Field Names
- ✅ Must exist in target table schema
- ✅ Must use proper field type prefixes (TXT_, NUM_, GID_, etc.)

### Form Tab IDs
- ✅ Must start with "TAB_" prefix
- ✅ Must be alphanumeric + underscore
- ❌ Remove orderIndex validation (handled by comma-separated list)

### Table Names
- ✅ Must be exactly 2 uppercase letters
- ✅ Must exist in database schema

## Consistency with MetadataModels.cs

The corrected API definitions now align with the Request Models in `MetadataModels.cs`:
- ✅ Proper authentication via `hostName`
- ✅ User tracking via `createdBy`, `modifiedBy`
- ✅ Section support for GLOBAL/user contexts
- ✅ Metadata-driven architecture support
- ✅ Proper validation attributes and data types

## Recommendations

1. **Update Documentation**: Replace misaligned properties with corrected versions
2. **API Implementation**: Ensure controllers handle metadata-based operations correctly
3. **Client Applications**: Update client code to work with actual metadata structure
4. **Performance**: Consider caching for expensive operations like record counts
5. **Testing**: Create tests that validate against actual metadata system behavior
