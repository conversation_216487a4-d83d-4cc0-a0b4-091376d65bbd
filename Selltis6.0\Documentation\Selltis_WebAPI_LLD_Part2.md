# Selltis 6.0 Web API Migration - Low-Level Design Document (Part 2)

**Continuation of API Design and Contract**

---

## 3.3 Manage Forms API (Continued)

### 3.3.1 Form Management Endpoints (Continued)

**Endpoint 34: Duplicate Form**

```
POST /api/forms/{id}/duplicate
```

**Path Parameters:**
- `id` (required): string - Source form ID

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "newFormId": "FRM_FORMCOPY",
  "newFormName": "Form Copy",
  "section": "GLOBAL",
  "copyFields": true,
  "copyTabs": true,
  "copyMetadata": true
}
```

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Form duplicated successfully",
  "sourceFormId": "FRM_COMPANY",
  "newFormId": "FRM_FORMCOPY",
  "createdDate": "2025-09-29T12:10:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid parameters or new ID already exists
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Source form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 35: Share/Unshare Form**

```
POST /api/forms/{id}/share
```

**Path Parameters:**
- `id` (required): string - Form ID

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "isShared": true,
  "section": "GLOBAL"
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Form sharing updated successfully",
  "formId": "FRM_COMPANY",
  "isShared": true
}
```

**Error Responses:**
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Authentication failed
- `403 Forbidden`: User lacks permission to share
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 36: Get Form Metadata**

```
GET /api/forms/{id}/metadata
```

**Path Parameters:**
- `id` (required): string - Form ID

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `section` (optional): string - Form section (default: "GLOBAL")

**Success Response (200 OK):**
```json
{
  "formId": "FRM_COMPANY",
  "section": "GLOBAL",
  "metadata": {
    "FILE": "CO",
    "NAME": "Company Form",
    "TYPE": "EDIT",
    "SHOWSTATUSBAR": "1",
    "STATUSBARFIELD": "TXT_CODE",
    "TABS": "TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS",
    "CUSTOMFIELD1": "value1"
  },
  "rawMetadata": "[Form metadata in INI format]"
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 37: Update Form Metadata**

```
PUT /api/forms/{id}/metadata
```

**Path Parameters:**
- `id` (required): string - Form ID

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "section": "GLOBAL",
  "metadata": {
    "CUSTOMFIELD1": "newvalue1",
    "SHOWSTATUSBAR": "0"
  }
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Form metadata updated successfully",
  "formId": "FRM_COMPANY",
  "modifiedDate": "2025-09-29T12:15:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid metadata format
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 38: Preview Form**

```
POST /api/forms/{id}/preview
```

**Path Parameters:**
- `id` (required): string - Form ID

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "recordId": "12345678-1234-1234-1234-123456789012",
  "mode": "EDIT",
  "section": "GLOBAL"
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "formId": "FRM_COMPANY",
  "previewUrl": "/Forms/Preview?formId=FRM_COMPANY&recordId=...",
  "htmlContent": "<div class='form-preview'>...</div>",
  "fieldValues": {
    "TXT_CODE": "ACME001",
    "TXT_COMPANYNAME": "Acme Corporation",
    "TEL_PHONENO": "555-1234"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form or record not found
- `500 Internal Server Error`: Server error

---

### 3.3.2 Form Field Management Endpoints (8)

> **🏗️ SELLTIS METADATA ARCHITECTURE - FORM FIELDS**
>
> **Key Architectural Facts:**
> - Form fields are stored as sequential metadata properties: `FIELD1`, `FIELD2`, `FIELD3`, etc.
> - Layout properties (rowNumber, columnNumber, colSpan, etc.) are **NOT stored in metadata**
> - UI properties (labelWidth, controlWidth, state, tooltip) are **NOT stored in metadata**
> - Field validation properties (isRequired, isReadOnly) are **table schema properties**, not form metadata
> - Tab assignments are derived from the `TABS` comma-separated metadata property
> - Field positioning is managed through the sequence order in metadata, not coordinates
>
> **What IS stored in form metadata:**
> - Field sequence: `FIELD1=TXT_CODE`, `FIELD2=TXT_COMPANYNAME`, etc.
> - Tab definitions: `TABS=TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS`
> - Form properties: `NAME`, `TYPE`, `FILE`, `SHOWSTATUSBAR`, etc.
>
> **What is NOT stored in form metadata:**
> - Individual field layout coordinates or sizing
> - UI state properties or visual formatting
> - Field validation rules (these come from table schema)

**Endpoint 39: Get Form Fields**

```
GET /api/forms/{formId}/fields
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `section` (optional): string - Form section (default: "GLOBAL")
- `tabId` (optional): string - Filter by tab ID

**Success Response (200 OK):**

> **⚠️ ARCHITECTURE NOTE**: Form fields are stored as FIELD1, FIELD2, etc. in Selltis metadata, not as individual layout objects with positioning properties.

```json
// ❌ INCORRECT - Layout properties not stored in metadata
{
  "formId": "FRM_COMPANY",
  "fields": [
    {
      "fieldName": "TXT_CODE",
      "fieldLabel": "Company Code",
      "fieldType": "TXT",
      "tabId": "TAB_GENERAL",
      // "rowNumber": 1,           // ❌ NOT in metadata - UI layout property
      // "columnNumber": 1,        // ❌ NOT in metadata - UI layout property
      // "colSpan": 1,            // ❌ NOT in metadata - UI layout property
      // "rowSpan": 1,            // ❌ NOT in metadata - UI layout property
      // "state": 0,              // ❌ NOT in metadata - UI state property
      // "labelAbove": false,     // ❌ NOT in metadata - UI layout property
      // "labelWidth": 150,       // ❌ NOT in metadata - UI layout property
      // "controlWidth": 200,     // ❌ NOT in metadata - UI layout property
      // "isRequired": true,      // ❌ NOT in form metadata - table schema property
      // "isReadOnly": false,     // ❌ NOT in form metadata - UI state property
      // "isVisible": true,       // ❌ NOT in form metadata - UI state property
      // "tooltip": "Enter company code",  // ❌ NOT in metadata - UI property
      "defaultValue": "",
      "validationRule": ""
    }
  ],
  "totalCount": 25
}
```

**✅ CORRECTED Success Response (200 OK):**
```json
{
  "formId": "FRM_COMPANY",
  "section": "GLOBAL",
  "tableName": "CO",
  "fields": [
    {
      "fieldName": "TXT_CODE",
      "fieldOrder": 1,                    // Position in FIELD1, FIELD2, etc.
      "tabAssignment": "TAB_GENERAL"      // From tab analysis if tabs exist
    },
    {
      "fieldName": "TXT_COMPANYNAME",
      "fieldOrder": 2,
      "tabAssignment": "TAB_GENERAL"
    }
  ],
  "tabs": ["TAB_GENERAL", "TAB_CONTACT", "TAB_ADDRESS"],  // From TABS metadata
  "totalFieldCount": 25,
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z",
  "createdBy": "12345678-1234-1234-1234-123456789012",
  "modifiedBy": "ADMIN"
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 40: Add Field to Form**

```
POST /api/forms/{formId}/fields
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Form fields are added to metadata as FIELD1, FIELD2, etc. Layout properties are not stored in metadata.

```json
// ❌ INCORRECT - Layout properties not stored in metadata
{
  "hostName": "demo.selltis.com",
  "fieldName": "EML_EMAIL",
  "tabId": "TAB_CONTACT",
  // "rowNumber": 5,           // ❌ NOT in metadata - UI layout property
  // "columnNumber": 1,        // ❌ NOT in metadata - UI layout property
  // "colSpan": 2,            // ❌ NOT in metadata - UI layout property
  // "rowSpan": 1,            // ❌ NOT in metadata - UI layout property
  // "state": 0,              // ❌ NOT in metadata - UI state property
  // "labelAbove": false,     // ❌ NOT in metadata - UI layout property
  // "labelWidth": 150,       // ❌ NOT in metadata - UI layout property
  // "controlWidth": 300,     // ❌ NOT in metadata - UI layout property
  "section": "GLOBAL"
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "fieldName": "EML_EMAIL",
  "section": "GLOBAL",
  "insertAfterField": "TXT_COMPANYNAME",  // Position relative to existing field
  "tabId": "TAB_CONTACT"                  // Tab assignment (if tabs exist)
}
```

**Validation Rules:**
- `fieldName`: Required, must exist in table schema
- `tabId`: Optional, must exist in form if specified
- `insertAfterField`: Optional, field positioning relative to existing field

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Field added to form successfully",
  "formId": "FRM_COMPANY",
  "fieldName": "EML_EMAIL",
  "createdDate": "2025-09-29T12:20:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors or field already exists in form
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form or tab not found
- `409 Conflict`: Field already exists in form
- `500 Internal Server Error`: Server error

---

**Endpoint 41: Update Field Properties**

```
PUT /api/forms/{formId}/fields/{fieldId}
```

**Path Parameters:**
- `formId` (required): string - Form ID
- `fieldId` (required): string - Field name

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Field properties in forms are limited to metadata-stored values. Layout and UI properties are not stored in metadata.

```json
// ❌ INCORRECT - UI properties not stored in metadata
{
  "hostName": "demo.selltis.com",
  // "labelWidth": 200,       // ❌ NOT in metadata - UI layout property
  // "controlWidth": 350,     // ❌ NOT in metadata - UI layout property
  // "state": 0,              // ❌ NOT in metadata - UI state property
  // "tooltip": "Updated tooltip text",  // ❌ NOT in metadata - UI property
  // "isRequired": true,      // ❌ NOT in form metadata - table schema property
  "section": "GLOBAL"
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "section": "GLOBAL",
  "newPosition": 5,                    // Update field order in FIELD1, FIELD2, etc.
  "tabId": "TAB_CONTACT"              // Move to different tab
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Field properties updated successfully",
  "formId": "FRM_COMPANY",
  "fieldName": "EML_EMAIL",
  "modifiedDate": "2025-09-29T12:25:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form or field not found
- `500 Internal Server Error`: Server error

---

**Endpoint 42: Remove Field from Form**

```
DELETE /api/forms/{formId}/fields/{fieldId}
```

**Path Parameters:**
- `formId` (required): string - Form ID
- `fieldId` (required): string - Field name

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `section` (optional): string - Form section (default: "GLOBAL")

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Field removed from form successfully",
  "formId": "FRM_COMPANY",
  "fieldName": "EML_EMAIL"
}
```

**Error Responses:**
- `400 Bad Request`: Cannot remove required system field
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form or field not found
- `500 Internal Server Error`: Server error

---

**Endpoint 43: Update Field Position**

```
PUT /api/forms/{formId}/fields/{fieldId}/position
```

**Path Parameters:**
- `formId` (required): string - Form ID
- `fieldId` (required): string - Field name

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Field positioning in Selltis is managed through FIELD1, FIELD2, etc. sequence in metadata, not grid coordinates.

```json
// ❌ INCORRECT - Grid positioning not stored in metadata
{
  "hostName": "demo.selltis.com",
  "tabId": "TAB_CONTACT",
  // "rowNumber": 3,          // ❌ NOT in metadata - UI layout property
  // "columnNumber": 2,       // ❌ NOT in metadata - UI layout property
  // "colSpan": 1,           // ❌ NOT in metadata - UI layout property
  // "rowSpan": 1,           // ❌ NOT in metadata - UI layout property
  "section": "GLOBAL"
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "tabId": "TAB_CONTACT",
  "newFieldOrder": 3,                 // New position in FIELD1, FIELD2, etc.
  "insertAfterField": "TXT_PHONE",    // Position relative to existing field
  "section": "GLOBAL"
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Field position updated successfully",
  "formId": "FRM_COMPANY",
  "fieldName": "EML_EMAIL",
  "tabId": "TAB_CONTACT",
  "newFieldOrder": 3,                 // New position in metadata sequence
  "modifiedDate": "2025-09-29T12:25:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid position parameters
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form, field, or tab not found
- `500 Internal Server Error`: Server error

---

**Endpoint 44: Get Available Fields for Form**

```
GET /api/forms/{formId}/fields/available
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `section` (optional): string - Form section (default: "GLOBAL")

**Success Response (200 OK):**
```json
{
  "formId": "FRM_COMPANY",
  "tableName": "CO",
  "availableFields": [
    {
      "fieldName": "TXT_WEBSITE",
      "fieldLabel": "Website",
      "fieldType": "TXT",
      "isInForm": false
    },
    {
      "fieldName": "NUM_EMPLOYEES",
      "fieldLabel": "Number of Employees",
      "fieldType": "NUM",
      "isInForm": false
    }
  ],
  "totalAvailable": 15
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 45: Bulk Add/Update Fields**

```
POST /api/forms/{formId}/fields/bulk
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Bulk field operations work with metadata field sequences, not layout coordinates.

```json
// ❌ INCORRECT - Layout coordinates not stored in metadata
{
  "hostName": "demo.selltis.com",
  "section": "GLOBAL",
  "operation": "add",
  "fields": [
    {
      "fieldName": "TXT_WEBSITE",
      "tabId": "TAB_CONTACT",
      // "rowNumber": 6,          // ❌ NOT in metadata - UI layout property
      // "columnNumber": 1,       // ❌ NOT in metadata - UI layout property
      // "colSpan": 2,           // ❌ NOT in metadata - UI layout property
      // "rowSpan": 1            // ❌ NOT in metadata - UI layout property
    }
  ]
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "section": "GLOBAL",
  "operation": "add",
  "fields": [
    {
      "fieldName": "TXT_WEBSITE",
      "tabId": "TAB_CONTACT",
      "insertAfterField": "EML_EMAIL"    // Position relative to existing field
    },
    {
      "fieldName": "NUM_EMPLOYEES",
      "tabId": "TAB_GENERAL",
      "insertAfterField": "TXT_COMPANYNAME"
    }
  ]
}
```

**Validation Rules:**
- `operation`: Required, one of: "add", "update", "delete"
- `fields`: Required, array of field configurations

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Bulk field operation completed successfully",
  "formId": "FRM_COMPANY",
  "processedCount": 2,
  "successCount": 2,
  "failureCount": 0,
  "results": [
    {
      "fieldName": "TXT_WEBSITE",
      "status": "success",
      "message": "Field added successfully"
    },
    {
      "fieldName": "NUM_EMPLOYEES",
      "status": "success",
      "message": "Field added successfully"
    }
  ]
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 46: Update Form Layout**

```
PUT /api/forms/{formId}/layout
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Form layout in Selltis is managed through field sequence (FIELD1, FIELD2, etc.) and tab assignments, not grid-based layouts.

```json
// ❌ INCORRECT - Grid-based layout not stored in metadata
{
  "hostName": "demo.selltis.com",
  "section": "GLOBAL",
  "layout": {
    // Grid-based layout structure not supported in Selltis metadata
  }
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "section": "GLOBAL",
  "fieldSequence": [
    "TXT_CODE",
    "TXT_COMPANYNAME",
    "MLS_ACCOUNTTYPE",
    "EML_EMAIL"
  ],
  "tabAssignments": {
    "TAB_GENERAL": ["TXT_CODE", "TXT_COMPANYNAME", "MLS_ACCOUNTTYPE"],
    "TAB_CONTACT": ["EML_EMAIL"]
  }
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Form layout updated successfully",
  "formId": "FRM_COMPANY",
  "modifiedDate": "2025-09-29T12:30:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid layout configuration
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

### 3.3.3 Form Tab Management Endpoints (4)

> **🏗️ SELLTIS METADATA ARCHITECTURE - FORM TABS**
>
> **Key Architectural Facts:**
> - Form tabs are stored as a **single comma-separated string** in the `TABS` metadata property
> - Example: `TABS=TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS`
> - Individual tab properties (tabName, fieldCount, isVisible, tooltip) are **NOT stored in metadata**
> - Tab order is determined by position in the comma-separated list
> - Tab names/labels are typically derived from the tab ID or stored separately in label metadata
>
> **What IS stored in form metadata:**
> - Tab sequence: `TABS=TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS`
> - Tab count can be derived from parsing the TABS property
>
> **What is NOT stored in form metadata:**
> - Individual tab names or labels
> - Tab visibility states or UI properties
> - Field counts per tab (calculated dynamically)
> - Tab-specific tooltips or descriptions

**Endpoint 47: Get Form Tabs**

```
GET /api/forms/{formId}/tabs
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `section` (optional): string - Form section (default: "GLOBAL")

**Success Response (200 OK):**

> **⚠️ ARCHITECTURE NOTE**: Form tabs are stored as comma-separated values in the TABS metadata property, not as individual objects with properties.

```json
// ❌ INCORRECT - Individual tab properties not stored in metadata
{
  "formId": "FRM_COMPANY",
  "tabs": [
    {
      "tabId": "TAB_GENERAL",
      // "tabName": "General",        // ❌ NOT stored separately in metadata
      // "orderIndex": 1,             // ❌ Derived from TABS comma-separated order
      // "fieldCount": 15,            // ❌ Calculated, not stored in metadata
      // "isVisible": true,           // ❌ NOT in metadata - UI property
      // "tooltip": "General company information"  // ❌ NOT in metadata
    }
  ],
  "totalCount": 2
}
```

**✅ CORRECTED Success Response (200 OK):**
```json
{
  "formId": "FRM_COMPANY",
  "section": "GLOBAL",
  "tabsMetadata": "TAB_GENERAL,TAB_CONTACT,TAB_ADDRESS",  // Raw TABS property
  "tabs": [
    {
      "tabId": "TAB_GENERAL",
      "orderIndex": 1              // Position in comma-separated list
    },
    {
      "tabId": "TAB_CONTACT",
      "orderIndex": 2
    },
    {
      "tabId": "TAB_ADDRESS",
      "orderIndex": 3
    }
  ],
  "totalTabCount": 3,
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z"
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `500 Internal Server Error`: Server error

---

**Endpoint 48: Add Tab to Form**

```
POST /api/forms/{formId}/tabs
```

**Path Parameters:**
- `formId` (required): string - Form ID

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Tabs are added to the comma-separated TABS metadata property, not as individual objects.

```json
// ❌ INCORRECT - Individual tab properties not stored in metadata
{
  "hostName": "demo.selltis.com",
  "tabId": "TAB_CUSTOM",
  // "tabName": "Custom Fields",     // ❌ NOT stored in metadata
  // "orderIndex": 3,                // ❌ Position handled by comma-separated list
  // "isVisible": true,              // ❌ NOT in metadata - UI property
  // "tooltip": "Custom field information",  // ❌ NOT in metadata
  "section": "GLOBAL"
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "tabId": "TAB_CUSTOM",
  "insertAfterTab": "TAB_CONTACT",  // Position relative to existing tab
  "section": "GLOBAL"
}
```

**Validation Rules:**
- `tabId`: Required, 2-20 characters, alphanumeric + underscore, must start with "TAB_"
- `insertAfterTab`: Optional, must exist in form if specified

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Tab added to form successfully",
  "formId": "FRM_COMPANY",
  "tabId": "TAB_CUSTOM",
  "createdDate": "2025-09-29T12:35:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors or tab ID already exists
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form not found
- `409 Conflict`: Tab with same ID already exists
- `500 Internal Server Error`: Server error

---

**Endpoint 49: Update Tab Properties**

```
PUT /api/forms/{formId}/tabs/{tabId}
```

**Path Parameters:**
- `formId` (required): string - Form ID
- `tabId` (required): string - Tab ID

**Request Body:**

> **⚠️ ARCHITECTURE NOTE**: Tab properties are limited to position changes in the TABS comma-separated list.

```json
// ❌ INCORRECT - Individual tab properties not stored in metadata
{
  "hostName": "demo.selltis.com",
  // "tabName": "Updated Tab Name",   // ❌ NOT stored in metadata
  // "orderIndex": 2,                 // ❌ Position handled by comma-separated list
  // "isVisible": true,               // ❌ NOT in metadata - UI property
  // "tooltip": "Updated tooltip",    // ❌ NOT in metadata
  "section": "GLOBAL"
}
```

**✅ CORRECTED Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "newPosition": 2,                   // New position in TABS comma-separated list
  "insertAfterTab": "TAB_GENERAL",    // Position relative to existing tab
  "section": "GLOBAL"
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Tab position updated successfully",
  "formId": "FRM_COMPANY",
  "tabId": "TAB_CUSTOM",
  "newPosition": 2,
  "updatedTabsMetadata": "TAB_GENERAL,TAB_CUSTOM,TAB_CONTACT,TAB_ADDRESS",
  "modifiedDate": "2025-09-29T12:40:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form or tab not found
- `500 Internal Server Error`: Server error

---

**Endpoint 50: Delete Tab from Form**

```
DELETE /api/forms/{formId}/tabs/{tabId}
```

**Path Parameters:**
- `formId` (required): string - Form ID
- `tabId` (required): string - Tab ID

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `section` (optional): string - Form section (default: "GLOBAL")
- `deleteFields` (optional): bool - Delete fields in tab (default: false)

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Tab deleted from form successfully",
  "formId": "FRM_COMPANY",
  "tabId": "TAB_CUSTOM",
  "fieldsDeleted": 5
}
```

**Error Responses:**
- `400 Bad Request`: Cannot delete last tab or system tab
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Form or tab not found
- `500 Internal Server Error`: Server error

---

### 3.4 Manage Databases API (18 Endpoints)

#### 3.4.1 Table Management Endpoints (8)

> **🏗️ SELLTIS METADATA ARCHITECTURE - DATABASE TABLES**
>
> **Key Architectural Facts:**
> - Table information comes from multiple sources: database schema, metadata system, and data queries
> - **Performance-sensitive properties** require expensive operations and should be retrieved separately:
>   - `recordCount`: Requires `COUNT(*)` query per table
>   - `hasForm`: Requires metadata system lookup for form existence
>   - `hasDesktop`: Requires metadata system lookup for desktop existence
> - Table metadata is stored in the MD (metadata) table system
> - Field information comes from database schema inspection
>
> **What is efficiently retrievable:**
> - Table names, labels, and basic properties from schema
> - Field definitions, types, and constraints from database schema
> - Primary key and relationship information
>
> **What requires expensive operations:**
> - Record counts (avoid in list operations)
> - Form/desktop existence checks (use separate endpoints)
> - Complex relationship analysis

**Endpoint 51: Get All Tables** [IMPLEMENTED]

```
POST /api/managedatabase/tables
```

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "searchValue": "C",
  "onlyTables": false
}
```

**Query Parameters:**
- `searchValue` (optional): string - Filter tables by name/label
- `onlyTables` (optional): bool - Exclude system tables (default: false)

**Success Response (200 OK):**
```json
{
  "tables": [
    "CO - Companies",
    "CN - Contacts",
    "CT - Contracts"
  ],
  "totalCount": 3
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `500 Internal Server Error`: Server error

**Implementation Status:** ✅ Fully Implemented in ManageDatabaseApiController

---

**Endpoint 52: Get Specific Table Details**

```
GET /api/managedatabase/tables/{name}
```

**Path Parameters:**
- `name` (required): string - Table name (2-character code)

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname

**Success Response (200 OK):**

> **⚠️ ARCHITECTURE NOTE**: Some table properties require expensive queries or metadata system lookups and should be retrieved via separate API calls.

```json
// ❌ PARTIALLY INCORRECT - Some properties require expensive operations
{
  "tableName": "CO",
  "tableLabel": "Companies",
  "tableLabelPlural": "Companies",
  "fieldCount": 45,
  // "recordCount": 1250,         // ❌ Requires expensive COUNT(*) query
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z",
  // "hasPermissions": true,      // ❌ Requires metadata system lookup
  // "hasForm": true,            // ❌ Requires metadata system lookup
  // "hasDesktop": true,         // ❌ Requires metadata system lookup
  "primaryKeyField": "GID_COMPANY",
  "fields": [
    {
      "fieldName": "GID_COMPANY",
      "fieldLabel": "Company ID",
      "fieldType": "GID",
      "length": 36,
      "isRequired": true,
      "isPrimaryKey": true
    },
    {
      "fieldName": "TXT_CODE",
      "fieldLabel": "Company Code",
      "fieldType": "TXT",
      "length": 50,
      "isRequired": true,
      "isPrimaryKey": false
    }
  ]
}
```

**✅ CORRECTED Success Response (200 OK):**
```json
{
  "tableName": "CO",
  "tableLabel": "Companies",
  "tableLabelPlural": "Companies",
  "fieldCount": 45,
  "primaryKeyField": "GID_COMPANY",
  "fields": [
    {
      "fieldName": "GID_COMPANY",
      "fieldLabel": "Company ID",
      "fieldType": "GID",
      "length": 36,
      "isRequired": true,
      "isPrimaryKey": true
    },
    {
      "fieldName": "TXT_CODE",
      "fieldLabel": "Company Code",
      "fieldType": "TXT",
      "length": 50,
      "isRequired": true,
      "isPrimaryKey": false
    }
  ],
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z"
  // Note: hasForm, hasDesktop, recordCount require separate API calls
  // due to performance and architectural considerations
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

---

**Endpoint 53: Create New Table** [IMPLEMENTED]

```
POST /api/managedatabase/tables/create
```

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "tableName": "PR",
  "tableLabel": "Products",
  "tableLabelPlural": "Products",
  "createFileLabels": true,
  "createLinks": true,
  "createFieldLabels": true,
  "createForm": true,
  "createDesktop": true,
  "createPermissions": true
}
```

**Validation Rules:**
- `tableName`: Required, exactly 2 uppercase letters
- `tableLabel`: Required, 1-50 characters
- `tableLabelPlural`: Required, 1-50 characters

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Table created successfully",
  "tableName": "PR",
  "createdDate": "2025-09-29T13:00:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors or invalid table name format
- `401 Unauthorized`: Authentication failed
- `409 Conflict`: Table already exists
- `500 Internal Server Error`: Server error

**Implementation Status:** ✅ Fully Implemented in ManageDatabaseApiController

---

**Endpoint 54: Update Table Properties**

```
PUT /api/managedatabase/tables/{name}
```

**Path Parameters:**
- `name` (required): string - Table name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "tableLabel": "Updated Label",
  "tableLabelPlural": "Updated Labels",
  "description": "Updated description"
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Table properties updated successfully",
  "tableName": "CO",
  "modifiedDate": "2025-09-29T13:05:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

---

**Endpoint 55: Delete Table**

```
DELETE /api/managedatabase/tables/{name}
```

**Path Parameters:**
- `name` (required): string - Table name

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `deleteData` (optional): bool - Delete all data (default: false)
- `deleteMetadata` (optional): bool - Delete metadata (default: true)

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Table deleted successfully",
  "tableName": "PR",
  "recordsDeleted": 0
}
```

**Error Responses:**
- `400 Bad Request`: Cannot delete system table
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

---

**Endpoint 56: Duplicate Table**

```
POST /api/managedatabase/tables/{name}/duplicate
```

**Path Parameters:**
- `name` (required): string - Source table name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "newTableName": "C2",
  "newTableLabel": "Companies Copy",
  "newTableLabelPlural": "Companies Copy",
  "copyData": false,
  "copyMetadata": true,
  "copyPermissions": true
}
```

**Validation Rules:**
- `newTableName`: Required, exactly 2 uppercase letters, must not exist
- `newTableLabel`: Required, 1-50 characters

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Table duplicated successfully",
  "sourceTableName": "CO",
  "newTableName": "C2",
  "recordsCopied": 0,
  "createdDate": "2025-09-29T13:10:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Source table not found
- `409 Conflict`: New table name already exists
- `500 Internal Server Error`: Server error

---

**Endpoint 57: Get Table Metadata**

```
GET /api/managedatabase/tables/{name}/metadata
```

**Path Parameters:**
- `name` (required): string - Table name

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname

**Success Response (200 OK):**

> **⚠️ ARCHITECTURE NOTE**: Table metadata is correctly retrieved from the metadata system. This endpoint is properly aligned with Selltis architecture.

```json
{
  "tableName": "CO",
  "section": "GLOBAL",
  "metadata": {
    "LABEL": "Companies",                    // ✅ Actual metadata property
    "LABELPLURAL": "Companies",             // ✅ Actual metadata property
    "DESCRIPTION": "Company master table",  // ✅ Actual metadata property
    "ICON": "building",                     // ✅ Actual metadata property
    "COLOR": "#0066CC",                     // ✅ Actual metadata property
    "DEFAULTSORT": "TXT_CODE",             // ✅ Actual metadata property
    "DEFAULTFILTER": "",                   // ✅ Actual metadata property
    "AUDITTRAIL": "1"                      // ✅ Actual metadata property
  },
  "createdDate": "2020-01-15T10:00:00Z",
  "modifiedDate": "2025-09-20T14:30:00Z",
  "createdBy": "12345678-1234-1234-1234-123456789012",
  "modifiedBy": "ADMIN",
  "product": "SA"
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

---

**Endpoint 58: Update Table Metadata**

```
PUT /api/managedatabase/tables/{name}/metadata
```

**Path Parameters:**
- `name` (required): string - Table name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "metadata": {
    "DESCRIPTION": "Updated description",
    "ICON": "building-o",
    "COLOR": "#0099FF",
    "AUDITTRAIL": "1"
  }
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Table metadata updated successfully",
  "tableName": "CO",
  "modifiedDate": "2025-09-29T13:15:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid metadata format
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

---

#### 3.4.2 Field Management Endpoints (6)

**Endpoint 59: Get Table Fields** [IMPLEMENTED]

```
POST /api/managedatabase/tables/{tableName}/fields
```

**Path Parameters:**
- `tableName` (required): string - Table name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com"
}
```

**Success Response (200 OK):**
```json
{
  "tableName": "CO",
  "fields": [
    {
      "fieldName": "GID_COMPANY",
      "fieldLabel": "Company ID",
      "fieldType": "GID",
      "length": 36,
      "isRequired": true,
      "isPrimaryKey": true,
      "defaultValue": null
    },
    {
      "fieldName": "TXT_CODE",
      "fieldLabel": "Company Code",
      "fieldType": "TXT",
      "length": 50,
      "isRequired": true,
      "isPrimaryKey": false,
      "defaultValue": null
    }
  ],
  "totalCount": 45
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

**Implementation Status:** ✅ Fully Implemented in ManageDatabaseApiController

---

**Endpoint 60: Add Field to Table** [PARTIAL]

```
POST /api/managedatabase/tables/{tableName}/fields/add
```

**Path Parameters:**
- `tableName` (required): string - Table name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "fieldName": "TXT_WEBSITE",
  "fieldLabel": "Website",
  "fieldType": "TXT",
  "length": 255,
  "isRequired": false,
  "defaultValue": null,
  "createFieldLabel": true
}
```

**Validation Rules:**
- `fieldName`: Required, 3-50 characters, must start with field type prefix (TXT_, NUM_, etc.)
- `fieldLabel`: Required, 1-100 characters
- `fieldType`: Required, one of: TXT, NUM, DTT, GID, MLS, LNK, etc.
- `length`: Required for TXT fields

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Field added to table successfully",
  "tableName": "CO",
  "fieldName": "TXT_WEBSITE",
  "createdDate": "2025-09-29T13:20:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors or invalid field type
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `409 Conflict`: Field already exists
- `500 Internal Server Error`: Server error

**Implementation Status:** 🟡 Partially Implemented in ManageDatabaseApiController

---

**Endpoint 61: Update Field Properties**

```
PUT /api/managedatabase/tables/{tableName}/fields/{fieldName}
```

**Path Parameters:**
- `tableName` (required): string - Table name
- `fieldName` (required): string - Field name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "fieldLabel": "Updated Label",
  "length": 300,
  "isRequired": true,
  "defaultValue": "https://"
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Field properties updated successfully",
  "tableName": "CO",
  "fieldName": "TXT_WEBSITE",
  "modifiedDate": "2025-09-29T13:25:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors or cannot modify system field
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table or field not found
- `500 Internal Server Error`: Server error

---

**Endpoint 62: Delete Field from Table**

```
DELETE /api/managedatabase/tables/{tableName}/fields/{fieldName}
```

**Path Parameters:**
- `tableName` (required): string - Table name
- `fieldName` (required): string - Field name

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `deleteData` (optional): bool - Delete field data (default: true)

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Field deleted from table successfully",
  "tableName": "CO",
  "fieldName": "TXT_WEBSITE"
}
```

**Error Responses:**
- `400 Bad Request`: Cannot delete system field or primary key
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table or field not found
- `500 Internal Server Error`: Server error

---

**Endpoint 63: Get Available Field Types**

```
GET /api/managedatabase/fields/types
```

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname

**Success Response (200 OK):**
```json
{
  "fieldTypes": [
    {
      "typeCode": "TXT",
      "typeName": "Text",
      "description": "Single-line text field",
      "requiresLength": true,
      "maxLength": 8000,
      "prefix": "TXT_"
    },
    {
      "typeCode": "NUM",
      "typeName": "Number",
      "description": "Numeric field (decimal)",
      "requiresLength": false,
      "prefix": "NUM_"
    },
    {
      "typeCode": "DTT",
      "typeName": "Date/Time",
      "description": "Date and time field",
      "requiresLength": false,
      "prefix": "DTT_"
    },
    {
      "typeCode": "GID",
      "typeName": "GUID",
      "description": "Globally unique identifier",
      "requiresLength": false,
      "prefix": "GID_"
    },
    {
      "typeCode": "MLS",
      "typeName": "Multi-line String",
      "description": "Multi-line text field",
      "requiresLength": false,
      "prefix": "MLS_"
    },
    {
      "typeCode": "LNK",
      "typeName": "Link",
      "description": "Link to another table",
      "requiresLength": false,
      "requiresTargetTable": true,
      "prefix": "LNK_"
    }
  ],
  "totalCount": 6
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `500 Internal Server Error`: Server error

---

**Endpoint 64: Bulk Field Operations**

```
POST /api/managedatabase/tables/{tableName}/fields/bulk
```

**Path Parameters:**
- `tableName` (required): string - Table name

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "operation": "add",
  "fields": [
    {
      "fieldName": "TXT_WEBSITE",
      "fieldLabel": "Website",
      "fieldType": "TXT",
      "length": 255,
      "isRequired": false
    },
    {
      "fieldName": "NUM_EMPLOYEES",
      "fieldLabel": "Number of Employees",
      "fieldType": "NUM",
      "isRequired": false
    }
  ]
}
```

**Validation Rules:**
- `operation`: Required, one of: "add", "update", "delete"
- `fields`: Required, array of field configurations

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Bulk field operation completed successfully",
  "tableName": "CO",
  "processedCount": 2,
  "successCount": 2,
  "failureCount": 0,
  "results": [
    {
      "fieldName": "TXT_WEBSITE",
      "status": "success",
      "message": "Field added successfully"
    },
    {
      "fieldName": "NUM_EMPLOYEES",
      "status": "success",
      "message": "Field added successfully"
    }
  ]
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found
- `500 Internal Server Error`: Server error

---

#### 3.4.3 Schema Management Endpoints (4)

**Endpoint 65: Get Database Schema**

```
GET /api/managedatabase/schema
```

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `includeFields` (optional): bool - Include field details (default: false)
- `includeRelationships` (optional): bool - Include relationships (default: false)

**Success Response (200 OK):**

> **⚠️ ARCHITECTURE NOTE**: Schema endpoint should avoid expensive operations like record counts and metadata system lookups for performance.

```json
// ❌ PARTIALLY INCORRECT - Some properties require expensive operations
{
  "schemaVersion": "6.0.1",
  "lastRefreshed": "2025-09-29T13:30:00Z",
  "tables": [
    {
      "tableName": "CO",
      "tableLabel": "Companies",
      "fieldCount": 45,
      // "recordCount": 1250,     // ❌ Requires expensive COUNT(*) query per table
      // "hasForm": true,         // ❌ Requires metadata system lookup per table
      // "hasDesktop": true,      // ❌ Requires metadata system lookup per table
      "fields": [
        {
          "fieldName": "GID_COMPANY",
          "fieldType": "GID",
          "isPrimaryKey": true
        }
      ],
      "relationships": [
        {
          "relatedTable": "CN",
          "relationType": "OneToMany",
          "foreignKey": "LNK_COMPANY"
        }
      ]
    }
  ],
  "totalTables": 45
}
```

**✅ CORRECTED Success Response (200 OK):**
```json
{
  "schemaVersion": "6.0.1",
  "lastRefreshed": "2025-09-29T13:30:00Z",
  "tables": [
    {
      "tableName": "CO",
      "tableLabel": "Companies",
      "fieldCount": 45,
      "primaryKeyField": "GID_COMPANY",
      "fields": [
        {
          "fieldName": "GID_COMPANY",
          "fieldType": "GID",
          "isPrimaryKey": true
        },
        {
          "fieldName": "TXT_CODE",
          "fieldType": "TXT",
          "isPrimaryKey": false
        }
      ],
      "relationships": [
        {
          "relatedTable": "CN",
          "relationType": "OneToMany",
          "foreignKey": "LNK_COMPANY"
        }
      ]
    }
  ],
  "totalTables": 45
  // Note: recordCount, hasForm, hasDesktop require separate API calls
  // Use /api/managedatabase/tables/{name} for detailed table information
  // Use /api/managedatabase/tables/{name}/metadata for form/desktop checks
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `500 Internal Server Error`: Server error

---

**Endpoint 66: Refresh Schema Cache**

```
POST /api/managedatabase/schema/refresh
```

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "refreshTables": true,
  "refreshFields": true,
  "refreshLinks": true
}
```

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Schema cache refreshed successfully",
  "tablesRefreshed": 45,
  "fieldsRefreshed": 2150,
  "linksRefreshed": 120,
  "refreshedDate": "2025-09-29T13:35:00Z"
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `500 Internal Server Error`: Server error

---

**Endpoint 67: Get Table Relationships**

```
GET /api/managedatabase/relationships
```

**Query Parameters:**
- `hostName` (required): string - Selltis instance hostname
- `tableName` (optional): string - Filter by specific table

**Success Response (200 OK):**
```json
{
  "relationships": [
    {
      "sourceTable": "CO",
      "sourceTableLabel": "Companies",
      "targetTable": "CN",
      "targetTableLabel": "Contacts",
      "relationshipType": "OneToMany",
      "foreignKeyField": "LNK_COMPANY",
      "cascadeDelete": false
    },
    {
      "sourceTable": "CO",
      "sourceTableLabel": "Companies",
      "targetTable": "CT",
      "targetTableLabel": "Contracts",
      "relationshipType": "OneToMany",
      "foreignKeyField": "LNK_COMPANY",
      "cascadeDelete": false
    }
  ],
  "totalCount": 2
}
```

**Error Responses:**
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Table not found (if tableName specified)
- `500 Internal Server Error`: Server error

---

**Endpoint 68: Create Table Relationship**

```
POST /api/managedatabase/relationships
```

**Request Body:**
```json
{
  "hostName": "demo.selltis.com",
  "sourceTable": "CO",
  "targetTable": "PR",
  "relationshipType": "OneToMany",
  "foreignKeyField": "LNK_COMPANY",
  "cascadeDelete": false,
  "createForeignKey": true
}
```

**Validation Rules:**
- `sourceTable`: Required, must exist
- `targetTable`: Required, must exist
- `relationshipType`: Required, one of: "OneToOne", "OneToMany", "ManyToMany"
- `foreignKeyField`: Required, must be LNK type field

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Table relationship created successfully",
  "sourceTable": "CO",
  "targetTable": "PR",
  "foreignKeyField": "LNK_COMPANY",
  "createdDate": "2025-09-29T13:40:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Validation errors or invalid relationship
- `401 Unauthorized`: Authentication failed
- `404 Not Found`: Source or target table not found
- `409 Conflict`: Relationship already exists
- `500 Internal Server Error`: Server error

---

## Conclusion

This document completes the API endpoint specifications for the Selltis 6.0 Web API migration project. All **68 RESTful endpoints** have been fully documented with:

- Complete URI specifications
- HTTP methods
- Request/response schemas **✅ CORRECTED for metadata architecture alignment**
- Validation rules
- Error responses
- Implementation status

### 🔧 Critical Architecture Corrections Applied

This document has been **updated to correct critical misalignments** with the Selltis metadata architecture:

**✅ Form Field Management (Endpoints 39-46):**
- ❌ Removed: Layout properties (rowNumber, columnNumber, colSpan, rowSpan, labelWidth, controlWidth)
- ❌ Removed: UI state properties (state, labelAbove, isRequired, isReadOnly, isVisible, tooltip)
- ✅ Added: Metadata-aligned field sequence and positioning approach

**✅ Form Tab Management (Endpoints 47-50):**
- ❌ Removed: Individual tab properties (tabName, orderIndex, fieldCount, isVisible, tooltip)
- ✅ Added: Comma-separated TABS metadata structure approach

**✅ Database Table Operations (Endpoints 52, 57, 65):**
- ❌ Commented out: Expensive properties (recordCount, hasForm, hasDesktop)
- ✅ Added: Performance notes and separate API call recommendations

**✅ Architecture Documentation:**
- Added comprehensive notes explaining actual Selltis metadata storage
- Clarified what IS and IS NOT stored in metadata
- Provided performance guidance for expensive operations

### Summary of Endpoints

**Manage Desktops/Views API:** 28 endpoints (documented in Selltis_WebAPI_LLD.md)
- Desktop Management: 12 endpoints
- View Management: 16 endpoints

**Manage Forms API:** 22 endpoints (documented in this file)
- Form Management: 10 endpoints
- Form Field Management: 8 endpoints
- Form Tab Management: 4 endpoints

**Manage Databases API:** 18 endpoints (documented in this file)
- Table Management: 8 endpoints
- Field Management: 6 endpoints
- Schema Management: 4 endpoints

### Next Steps

1. Review complete LLD documentation set
2. Obtain stakeholder approval
3. Begin Phase 1 implementation (Foundation)
4. Implement endpoints according to priority
5. Create comprehensive test suite
6. Deploy to staging environment
7. Conduct integration testing
8. Deploy to production

### Related Documents

- **Selltis_WebAPI_LLD.md** - Main document with architecture and Desktop/View API
- **Selltis_WebAPI_LLD_Complete.md** - Class and object design
- **Selltis_WebAPI_LLD_Implementation.md** - Business logic and deployment
- **Selltis_WebAPI_LLD_Summary.md** - Quick reference and implementation checklist
- **README_LLD.md** - Documentation index and navigation guide

---

**Document Version:** 1.0
**Last Updated:** 2025-09-29
**Status:** Complete

